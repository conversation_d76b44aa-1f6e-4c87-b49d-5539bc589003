name: Build and Deploy Production Website to AWS S3 Bucket and Cloudfront

on:
  push:
    branches:
      - main

jobs:
  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.REGION }}

      - name: Install Dependencies
        run: |
          node --version
          yarn install --frozen-lockfile

      - name: Build Static Website
        run: yarn build
        env:
          VITE_APP_BASE_URL: ${{ secrets.VITE_APP_BASE_URL_PROD }}

      - name: Copy files to the production website with the AWS CLI
        run: |
          aws s3 sync --delete dist s3://${{ secrets.PROD_AWS_BUCKET }}

      - name: Invalidate Cloudfront cache
        run: |
          aws cloudfront create-invalidation \
          --distribution-id ${{ secrets.PROD_DIST_ID }} \
          --paths "/*"
